package admincommands;

import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.utils.chathandlers.AdminCommand;
import com.aionemu.gameserver.world.World;

/**
 * Admin command to clear ENEMY_OF_ALL_PLAYERS state from players
 * 
 * Usage:
 * //clearenemystate - Clear ENEMY_OF_ALL_PLAYERS state from yourself
 * //clearenemystate all - Clear ENEMY_OF_ALL_PLAYERS state from all online players
 * //clearenemystate check - Check which players have ENEMY_OF_ALL_PLAYERS state
 * 
 * <AUTHOR>
 */
public class ClearEnemyState extends AdminCommand {

    public ClearEnemyState() {
        super("clearenemystate", "Clears ENEMY_OF_ALL_PLAYERS state from players.");
    }

    @Override
    public void execute(Player admin, String... params) {
        if (params.length == 0) {
            // Clear state from admin
            clearEnemyState(admin);
            sendInfo(admin, "Cleared ENEMY_OF_ALL_PLAYERS state from yourself.");
            return;
        }

        String action = params[0].toLowerCase();
        
        switch (action) {
            case "all":
                clearAllPlayersEnemyState(admin);
                break;
            case "check":
                checkPlayersWithEnemyState(admin);
                break;
            default:
                sendInfo(admin, "Usage:");
                sendInfo(admin, "//clearenemystate - Clear from yourself");
                sendInfo(admin, "//clearenemystate all - Clear from all online players");
                sendInfo(admin, "//clearenemystate check - Check which players have the state");
                break;
        }
    }

    private void clearEnemyState(Player player) {
        if (player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
            player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            player.getController().onChangedPlayerAttributes();
        }
    }

    private void clearAllPlayersEnemyState(Player admin) {
        int clearedCount = 0;
        
        for (Player player : World.getInstance().getAllPlayers()) {
            if (player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
                player.unsetCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
                clearedCount++;
            }
        }
        
        sendInfo(admin, "Cleared ENEMY_OF_ALL_PLAYERS state from " + clearedCount + " players.");
    }

    private void checkPlayersWithEnemyState(Player admin) {
        int count = 0;
        StringBuilder players = new StringBuilder();
        
        for (Player player : World.getInstance().getAllPlayers()) {
            if (player.isInCustomState(CustomPlayerState.ENEMY_OF_ALL_PLAYERS)) {
                if (count > 0) {
                    players.append(", ");
                }
                players.append(player.getName());
                count++;
            }
        }
        
        if (count == 0) {
            sendInfo(admin, "No players currently have ENEMY_OF_ALL_PLAYERS state.");
        } else {
            sendInfo(admin, "Players with ENEMY_OF_ALL_PLAYERS state (" + count + "): " + players.toString());
        }
    }
}
