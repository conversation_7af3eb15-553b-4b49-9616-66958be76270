package ai;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.aionemu.gameserver.ai.AIName;
import com.aionemu.gameserver.ai.NpcAI;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.model.ChatType;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.network.aion.serverpackets.SM_MESSAGE;
import com.aionemu.gameserver.services.RechargerService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.PositionUtil;

/**
 * Recharger Healing NPC AI - Provides healing and cooldown reset services
 * 
 * <AUTHOR>
 */
@AIName("recharger_healing")
public class RechargerHealingAI extends NpcAI {

    // Track players who have been notified to prevent spam
    private final Map<Integer, Long> notifiedPlayers = new ConcurrentHashMap<>();
    private static final long NOTIFICATION_COOLDOWN = 30000; // 30 seconds between notifications

    public RechargerHealingAI(Npc owner) {
        super(owner);
    }

    @Override
    protected void handleSpawned() {
        super.handleSpawned();
        // Clean up old notifications periodically
        cleanupOldNotifications();
    }

    @Override
    protected void handleDialogStart(Player player) {
        // Disable clicking - NPC is proximity-based only
        // No dialog interaction allowed
        return;
    }

    @Override
    protected void handleCreatureSee(Creature creature) {
        if (!(creature instanceof Player)) {
            return;
        }

        Player player = (Player) creature;
        if (player.isDead()) {
            return;
        }

        // Check if player is within 3 meters for proximity healing
        if (PositionUtil.isInRange(getOwner(), player, 3.0f)) {
            tryProximityHealing(player);
        }

        // Check if we should notify this player about services
        if (shouldNotifyPlayer(player)) {
            notifyPlayer(player);
        }
    }

    @Override
    protected void handleCreatureMoved(Creature creature) {
        if (!(creature instanceof Player)) {
            return;
        }

        Player player = (Player) creature;
        if (player.isDead()) {
            return;
        }

        // Check if player is within 3 meters for proximity healing
        if (PositionUtil.isInRange(getOwner(), player, 3.0f)) {
            tryProximityHealing(player);
        }

        // Check if we should notify this player about services
        if (shouldNotifyPlayer(player)) {
            notifyPlayer(player);
        }
    }

    /**
     * Try to provide proximity-based healing to the player
     */
    private void tryProximityHealing(Player player) {
        if (!CustomConfig.RECHARGER_ENABLE) {
            return;
        }

        if (player.getLifeStats().isAboutToDie() || player.isDead()) {
            return;
        }

        if (player.getController().isInCombat()) {
            return;
        }

        // Check if player can use the service (cooldown check)
        if (!RechargerService.getInstance().canUseHealingService(player)) {
            return; // Don't spam messages, just silently wait
        }

        // Provide the healing service
        RechargerService.getInstance().useHealingService(player);

        // Send confirmation message
        String message;
        if (CustomConfig.RECHARGER_COST > 0) {
            message = String.format("Proximity healing activated! Your health and mana have been restored, and all cooldowns have been reset for %,d Kinah!",
                CustomConfig.RECHARGER_COST);
        } else {
            message = "Proximity healing activated! Your health and mana have been restored, and all cooldowns have been reset!";
        }
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner(), message, ChatType.NPC));
    }

    /**
     * Check if we should notify the player about our services
     */
    private boolean shouldNotifyPlayer(Player player) {
        if (!getOwner().canSee(player)) {
            return false;
        }

        // Check if player is within range
        if (!PositionUtil.isInRange(getOwner(), player, 10)) {
            return false;
        }

        // Check notification cooldown
        Long lastNotified = notifiedPlayers.get(player.getObjectId());
        if (lastNotified != null && (System.currentTimeMillis() - lastNotified) < NOTIFICATION_COOLDOWN) {
            return false;
        }

        return true;
    }

    /**
     * Notify player about available services
     */
    private void notifyPlayer(Player player) {
        notifiedPlayers.put(player.getObjectId(), System.currentTimeMillis());
        
        String message;
        if (CustomConfig.RECHARGER_COST > 0) {
            message = String.format("Greetings, traveler! I can restore your health, mana, and reset all your cooldowns for %,d Kinah. Simply stand within 3 meters of me to receive my services.",
                CustomConfig.RECHARGER_COST);
        } else {
            message = "Greetings, traveler! I can restore your health, mana, and reset all your cooldowns free of charge. Simply stand within 3 meters of me to receive my services.";
        }
        
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner(), message, ChatType.NPC));
    }

    /**
     * Clean up old notification entries
     */
    private void cleanupOldNotifications() {
        long currentTime = System.currentTimeMillis();
        notifiedPlayers.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > NOTIFICATION_COOLDOWN * 2);
    }

    @Override
    protected void handleDespawned() {
        super.handleDespawned();
        // Clean up when NPC despawns
        notifiedPlayers.clear();
    }
}