package playercommands;

import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.RechargerService;
import com.aionemu.gameserver.utils.chathandlers.PlayerCommand;

/**
 * Recharger player command - allows players to teleport to healing sanctuary
 * 
 * Usage:
 * .recharger - teleport to Recharger map
 * .recharger leave - return to bind point
 * 
 * <AUTHOR>
 */
public class Recharger extends PlayerCommand {

    public Recharger() {
        super("recharger", "Teleport to the Recharger sanctuary for healing and cooldown reset services.");
        setSyntaxInfo(
            "<.recharger> - Teleport to the Recharger sanctuary",
            "<.recharger leave> - Return to your bind point"
        );
    }

    @Override
    public void execute(Player player, String... params) {
        if (!RechargerService.getInstance().isEnabled()) {
            sendInfo(player, "The Recharger system is currently disabled.");
            return;
        }

        // Handle subcommands
        if (params.length > 0) {
            String subCommand = params[0].toLowerCase();
            
            switch (subCommand) {
                case "leave":
                case "exit":
                case "return":
                    RechargerService.getInstance().returnToBindPoint(player);
                    break;
                    
                default:
                    sendInfo(player, "Unknown subcommand: " + subCommand);
                    sendInfo(player, "Available commands:");
                    sendInfo(player, ".recharger - Teleport to Recharger sanctuary");
                    sendInfo(player, ".recharger leave - Return to bind point");
                    break;
            }
        } else {
            // No parameters - teleport to Recharger map
            RechargerService.getInstance().teleportToRecharger(player);
        }
    }
}