package zone;

import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.world.zone.ZoneInstance;
import com.aionemu.gameserver.world.zone.handler.AdvancedZoneHandler;
import com.aionemu.gameserver.world.zone.handler.ZoneNameAnnotation;

/**
 * Recharger Zone Handler - Makes the Recharger map a neutral safe zone
 * 
 * Features:
 * - <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are neutral to each other (can't attack)
 * - Players can still send duel requests to each other
 * - Safe zone for healing and cooldown reset services
 * 
 * <AUTHOR>
 */
@ZoneNameAnnotation(value = "RECHARGER_SANCTUARY_320150000")
public class RechargerZone implements AdvancedZoneHandler {

    @Override
    public void onEnterZone(Creature creature, ZoneInstance zone) {
        if (!(creature instanceof Player)) {
            return;
        }

        Player player = (Player) creature;

        // Welcome message
        PacketSendUtility.sendMessage(player,
            "Welcome to the Recharger Sanctuary! This is a neutral zone where all races are at peace.");
        PacketSendUtility.sendMessage(player,
            "You can challenge other players to duels, but direct combat is not allowed.");

        // Set neutral state - this prevents faction-based PvP
        player.setCustomState(CustomPlayerState.NEUTRAL_TO_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();
    }

    @Override
    public void onLeaveZone(Creature creature, ZoneInstance zone) {
        if (!(creature instanceof Player)) {
            return;
        }
        
        Player player = (Player) creature;

        // Remove neutral state when leaving
        player.unsetCustomState(CustomPlayerState.NEUTRAL_TO_ALL_PLAYERS);
        player.getController().onChangedPlayerAttributes();

        // Farewell message
        PacketSendUtility.sendMessage(player,
            "You have left the Recharger Sanctuary. Normal PvP rules now apply.");
    }

    @Override
    public boolean onDie(Creature lastAttacker, Creature target, ZoneInstance zone) {
        // Allow deaths from duels, but this shouldn't happen much in a neutral zone
        return false; // Let default death handling occur
    }
}
