package com.aionemu.gameserver.services;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aionemu.gameserver.ai.AI;
import com.aionemu.gameserver.ai.AIEngine;
import com.aionemu.gameserver.configs.main.CustomConfig;
import com.aionemu.gameserver.instance.handlers.RechargerInstanceHandler;
import com.aionemu.gameserver.model.gameobjects.Npc;
import com.aionemu.gameserver.model.animations.ObjectDeleteAnimation;
import com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.model.templates.spawns.SpawnTemplate;
import com.aionemu.gameserver.network.aion.serverpackets.SM_ATTACK_STATUS;
import com.aionemu.gameserver.network.aion.serverpackets.SM_ITEM_COOLDOWN;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SKILL_COOLDOWN;
import com.aionemu.gameserver.services.instance.InstanceService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.spawnengine.SpawnEngine;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.world.World;
import com.aionemu.gameserver.world.WorldMap;
import com.aionemu.gameserver.world.WorldMapInstance;
import com.aionemu.gameserver.world.WorldMapType;

/**
 * Recharger Service - Provides healing and cooldown reset functionality
 * Players can teleport to a special map with an NPC that heals HP/MP and resets cooldowns
 * 
 * <AUTHOR>
 */
public class RechargerService {

    private static final Logger log = LoggerFactory.getLogger(RechargerService.class);
    private static RechargerService instance = new RechargerService();
    
    // Track players who have used the healing service to prevent abuse
    private final Map<Integer, Long> playerCooldowns = new ConcurrentHashMap<>();
    
    // Track spawned NPCs per instance
    private final Map<Integer, Npc> spawnedNpcs = new ConcurrentHashMap<>();

    // Track all spawned NPCs per instance (including additional NPCs)
    private final Map<Integer, java.util.List<Npc>> allSpawnedNpcs = new ConcurrentHashMap<>();

    // Shared instance for all players
    private WorldMapInstance sharedInstance = null;

    public static RechargerService getInstance() {
        return instance;
    }

    private RechargerService() {
        log.info("RechargerService initialized");
    }

    /**
     * Check if Recharger system is enabled
     */
    public boolean isEnabled() {
        return CustomConfig.RECHARGER_ENABLE;
    }

    /**
     * Check if player meets level requirements
     */
    public boolean canUseRecharger(Player player) {
        if (!isEnabled()) {
            return false;
        }
        
        if (player.getLevel() < CustomConfig.RECHARGER_MIN_LEVEL) {
            PacketSendUtility.sendMessage(player, "You must be at least level " + CustomConfig.RECHARGER_MIN_LEVEL + " to use the Recharger system.");
            return false;
        }
        
        return true;
    }

    /**
     * Teleport player to Recharger map
     */
    public void teleportToRecharger(Player player) {
        if (!canUseRecharger(player)) {
            return;
        }

        try {
            // Get or create the instance
            WorldMapInstance instance = getOrCreateRechargerInstance();
            if (instance == null) {
                PacketSendUtility.sendMessage(player, "Failed to access Recharger map. Please try again later.");
                return;
            }

            // Clear any stuck enemy states before teleporting (fixes red player issue)
            boolean hadEnemyState = player.isInCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
            if (hadEnemyState) {
                player.unsetCustomState(com.aionemu.gameserver.model.gameobjects.player.CustomPlayerState.ENEMY_OF_ALL_PLAYERS);
                player.getController().onChangedPlayerAttributes();
                PacketSendUtility.sendMessage(player, "Cleared stuck enemy state - you should now appear neutral to other players.");
            }

            // Teleport player to the spawn coordinates
            TeleportService.teleportTo(player, CustomConfig.RECHARGER_MAP_ID, instance.getInstanceId(),
                    CustomConfig.RECHARGER_SPAWN_X, CustomConfig.RECHARGER_SPAWN_Y, CustomConfig.RECHARGER_SPAWN_Z,
                    (byte) CustomConfig.RECHARGER_SPAWN_H);

            PacketSendUtility.sendMessage(player, "Welcome to the Recharger sanctuary! Find the healing NPC to restore your health and reset cooldowns.");
            
            // Spawn all NPCs if not already spawned
            spawnAllNpcs(instance);
            
        } catch (Exception e) {
            log.error("Error teleporting player {} to Recharger map", player.getName(), e);
            PacketSendUtility.sendMessage(player, "An error occurred while teleporting. Please try again.");
        }
    }

    /**
     * Return player to their bind location
     */
    public void returnToBindPoint(Player player) {
        try {
            TeleportService.moveToBindLocation(player);
            PacketSendUtility.sendMessage(player, "You have been returned to your bind point.");
        } catch (Exception e) {
            log.error("Error returning player {} to bind point", player.getName(), e);
            PacketSendUtility.sendMessage(player, "An error occurred while returning to bind point. Please try again.");
        }
    }

    /**
     * Get or create a shared Recharger instance for all players
     */
    private WorldMapInstance getOrCreateRechargerInstance() {
        try {
            // Return existing shared instance if available and not empty
            if (sharedInstance != null && !sharedInstance.getPlayersInside().isEmpty()) {
                return sharedInstance;
            }

            // Check if we have an existing instance with players
            WorldMap worldMap = World.getInstance().getWorldMap(CustomConfig.RECHARGER_MAP_ID);
            if (worldMap != null) {
                for (WorldMapInstance existingInstance : worldMap) {
                    if (!existingInstance.getPlayersInside().isEmpty()) {
                        sharedInstance = existingInstance;
                        return sharedInstance;
                    }
                }
            }

            WorldMapType worldMapType = WorldMapType.getWorld(CustomConfig.RECHARGER_MAP_ID);
            if (worldMapType == null) {
                log.error("Invalid Recharger map ID: {}", CustomConfig.RECHARGER_MAP_ID);
                return null;
            }

            // Create a new shared instance for Recharger (no monsters)
            // Use InstanceService with custom handler to create a clean instance with no spawns
            WorldMapInstance instance = InstanceService.getNextAvailableInstance(CustomConfig.RECHARGER_MAP_ID, 0, (byte) 0, RechargerInstanceHandler::new, 50, true);
            if (instance == null) {
                log.error("Could not create new instance for Recharger map {}", CustomConfig.RECHARGER_MAP_ID);
                return null;
            }

            sharedInstance = instance;
            log.debug("Created new shared Recharger instance {} for map {}", instance.getInstanceId(), CustomConfig.RECHARGER_MAP_ID);
            return instance;
        } catch (Exception e) {
            log.error("Error getting/creating Recharger instance", e);
            return null;
        }
    }

    /**
     * Spawn all NPCs in the Recharger instance
     */
    private void spawnAllNpcs(WorldMapInstance instance) {
        try {
            // Check if NPCs are already spawned in this instance
            if (allSpawnedNpcs.containsKey(instance.getInstanceId())) {
                return; // NPCs already exist
            }

            List<Npc> spawnedNpcList = new ArrayList<>();

            // Spawn the original healing NPC with recharger_healing AI
            SpawnTemplate healingNpcTemplate = SpawnEngine.newSingleTimeSpawn(CustomConfig.RECHARGER_MAP_ID,
                    CustomConfig.RECHARGER_NPC_ID, CustomConfig.RECHARGER_NPC_X, CustomConfig.RECHARGER_NPC_Y,
                    CustomConfig.RECHARGER_NPC_Z, (byte) CustomConfig.RECHARGER_NPC_H);
            Npc healingNpc = (Npc) SpawnEngine.spawnObject(healingNpcTemplate, instance.getInstanceId());
            if (healingNpc != null) {
                // Assign the recharger_healing AI to this NPC
                assignRechargerAI(healingNpc);
                spawnedNpcs.put(instance.getInstanceId(), healingNpc);
                spawnedNpcList.add(healingNpc);
                log.debug("Spawned healing NPC {} with recharger_healing AI in Recharger instance {}", healingNpc.getObjectId(), instance.getInstanceId());
            }

            // Spawn Dummy NPCs (700595) at multiple locations
            float[][] dummyLocations = {
                {607.4567f, 197.3274f, 66.41704f, 60},
                {609.7971f, 202.44307f, 66.125f, 60},
                {611.68634f, 206.55898f, 66.125f, 60},
                {613.9822f, 211.62186f, 66.125f, 60},
                {615.6348f, 217.26079f, 66.125f, 60}
            };

            for (float[] location : dummyLocations) {
                SpawnTemplate dummyTemplate = SpawnEngine.newSingleTimeSpawn(CustomConfig.RECHARGER_MAP_ID,
                        700595, location[0], location[1], location[2], (byte) location[3]);
                Npc dummyNpc = (Npc) SpawnEngine.spawnObject(dummyTemplate, instance.getInstanceId());
                if (dummyNpc != null) {
                    spawnedNpcList.add(dummyNpc);
                    log.debug("Spawned Dummy NPC {} at ({}, {}, {}) in Recharger instance {}",
                            dummyNpc.getObjectId(), location[0], location[1], location[2], instance.getInstanceId());
                }
            }

            // Spawn Elyos Stigma Master (203711)
            SpawnTemplate elyosStigmaTemplate = SpawnEngine.newSingleTimeSpawn(CustomConfig.RECHARGER_MAP_ID,
                    203711, 624.75305f, 216.74797f, 66.09381f, (byte) 60);
            Npc elyosStigmaNpc = (Npc) SpawnEngine.spawnObject(elyosStigmaTemplate, instance.getInstanceId());
            if (elyosStigmaNpc != null) {
                spawnedNpcList.add(elyosStigmaNpc);
                log.debug("Spawned Elyos Stigma Master {} in Recharger instance {}", elyosStigmaNpc.getObjectId(), instance.getInstanceId());
            }

            // Spawn Asmodian Stigma Master (204061)
            SpawnTemplate asmoStigmaTemplate = SpawnEngine.newSingleTimeSpawn(CustomConfig.RECHARGER_MAP_ID,
                    204061, 614.9486f, 194.20682f, 66.5f, (byte) 60);
            Npc asmoStigmaNpc = (Npc) SpawnEngine.spawnObject(asmoStigmaTemplate, instance.getInstanceId());
            if (asmoStigmaNpc != null) {
                spawnedNpcList.add(asmoStigmaNpc);
                log.debug("Spawned Asmodian Stigma Master {} in Recharger instance {}", asmoStigmaNpc.getObjectId(), instance.getInstanceId());
            }

            // Spawn Soul Healers (831074) at multiple locations with recharger_healing AI
            float[][] soulHealerLocations = {
                {506.9505f, 221.89314f, 66.631676f, 60},
                {613.993f, 232.2629f, 66.25f, 60}
            };

            for (float[] location : soulHealerLocations) {
                SpawnTemplate soulHealerTemplate = SpawnEngine.newSingleTimeSpawn(CustomConfig.RECHARGER_MAP_ID,
                        831074, location[0], location[1], location[2], (byte) location[3]);
                Npc soulHealerNpc = (Npc) SpawnEngine.spawnObject(soulHealerTemplate, instance.getInstanceId());
                if (soulHealerNpc != null) {
                    // Assign the recharger_healing AI to this NPC
                    assignRechargerAI(soulHealerNpc);
                    spawnedNpcList.add(soulHealerNpc);
                    log.debug("Spawned Soul Healer {} with recharger_healing AI at ({}, {}, {}) in Recharger instance {}",
                            soulHealerNpc.getObjectId(), location[0], location[1], location[2], instance.getInstanceId());
                }
            }

            // Store all spawned NPCs
            allSpawnedNpcs.put(instance.getInstanceId(), spawnedNpcList);
            log.info("Spawned {} NPCs in Recharger instance {}", spawnedNpcList.size(), instance.getInstanceId());

        } catch (Exception e) {
            log.error("Error spawning NPCs in instance {}", instance.getInstanceId(), e);
        }
    }

    /**
     * Assign the recharger_healing AI to an NPC
     */
    private void assignRechargerAI(Npc npc) {
        try {
            AI newAi = AIEngine.getInstance().newAI("recharger_healing", npc);
            if (newAi != null) {
                // Use reflection to set the AI field
                java.lang.reflect.Field aiField = npc.getClass().getSuperclass().getDeclaredField("ai");
                aiField.setAccessible(true);

                // Despawn and respawn the NPC to properly initialize the AI
                World.getInstance().despawn(npc, ObjectDeleteAnimation.NONE);
                aiField.set(npc, newAi);
                World.getInstance().spawn(npc); // properly init AI states

                log.debug("Successfully assigned recharger_healing AI to NPC {}", npc.getObjectId());
            } else {
                log.error("Failed to create recharger_healing AI for NPC {}", npc.getObjectId());
            }
        } catch (Exception e) {
            log.error("Error assigning recharger_healing AI to NPC {}: {}", npc.getObjectId(), e.getMessage(), e);
        }
    }

    /**
     * Check if player can use healing service (cooldown check)
     */
    public boolean canUseHealingService(Player player) {
        if (!isEnabled()) {
            return false;
        }

        Long lastUsed = playerCooldowns.get(player.getObjectId());
        if (lastUsed != null) {
            long timeSinceLastUse = (System.currentTimeMillis() - lastUsed) / 1000;
            if (timeSinceLastUse < CustomConfig.RECHARGER_NPC_COOLDOWN) {
                long remainingTime = CustomConfig.RECHARGER_NPC_COOLDOWN - timeSinceLastUse;
                PacketSendUtility.sendMessage(player, "You must wait " + remainingTime + " seconds before using the healing service again.");
                return false;
            }
        }

        return true;
    }

    /**
     * Provide healing and cooldown reset service to player
     */
    public void useHealingService(Player player) {
        if (!canUseHealingService(player)) {
            return;
        }

        try {
            // Check if player has enough kinah (if cost is configured)
            if (CustomConfig.RECHARGER_COST > 0) {
                if (player.getInventory().getKinah() < CustomConfig.RECHARGER_COST) {
                    PacketSendUtility.sendMessage(player, "You need " + CustomConfig.RECHARGER_COST + " Kinah to use this service.");
                    return;
                }
                player.getInventory().decreaseKinah(CustomConfig.RECHARGER_COST);
            }

            // Heal HP and MP to full
            player.getLifeStats().increaseHp(SM_ATTACK_STATUS.TYPE.HP, player.getLifeStats().getMaxHp(), player);
            player.getLifeStats().increaseMp(SM_ATTACK_STATUS.TYPE.HEAL_MP, player.getLifeStats().getMaxMp(), 0, SM_ATTACK_STATUS.LOG.MPHEAL);

            // Reset all skill cooldowns (with null checks like other services)
            if (player.getSkillCoolDowns() != null) {
                player.getSkillCoolDowns().clear();
                PacketSendUtility.sendPacket(player, new SM_SKILL_COOLDOWN(player.getSkillCoolDowns()));
            }

            // Reset item cooldowns
            if (player.getItemCoolDowns() != null) {
                player.getItemCoolDowns().clear();
                PacketSendUtility.sendPacket(player, new SM_ITEM_COOLDOWN(player.getItemCoolDowns()));
            }

            // Set cooldown to prevent abuse
            playerCooldowns.put(player.getObjectId(), System.currentTimeMillis());

            PacketSendUtility.sendMessage(player, "Your health and mana have been restored, and all cooldowns have been reset!");
            
        } catch (Exception e) {
            log.error("Error providing healing service to player {}", player.getName(), e);
            PacketSendUtility.sendMessage(player, "An error occurred while providing healing service. Please try again.");
        }
    }

    /**
     * Clean up expired cooldowns periodically
     */
    public void cleanupExpiredCooldowns() {
        long currentTime = System.currentTimeMillis();
        playerCooldowns.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) / 1000 > CustomConfig.RECHARGER_NPC_COOLDOWN);
    }

    /**
     * Remove NPCs when instance is destroyed
     */
    public void onInstanceDestroy(int instanceId) {
        spawnedNpcs.remove(instanceId);
        allSpawnedNpcs.remove(instanceId);
    }
}